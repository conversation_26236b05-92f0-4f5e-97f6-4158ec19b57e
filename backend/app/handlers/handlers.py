from pydantic import BaseModel
from typing import Dict, Any, Tuple, List, Optional
from backend.app.utils.tapd import tap_client
import os
import json
import asyncio
import httpx
import re
from pathlib import Path
from backend.app.service.cards import send_card_message
from backend.app.config.config import (
    BUG_EVALUATION_WORKSPACE_ID,
)
from fastapi import Request
from backend.app.service.description_socre import get_description_score
from backend.app.service.description_socre import DimensionFieldEvaluation
from backend.app.service.title_score import get_title_score
from backend.app.service.suggest.suggest_priority_field import get_priority_suggestion
from backend.app.service.suggest.suggest_severity_field import get_severity_suggestion
from backend.app.service.title_score import TitleFieldEvaluation
from backend.app.service.suggest.field_evaluation_models import SuggestFieldEvaluation
from backend.app.service.bug_evaluation_service import save_bug_evaluation
from backend.app.utils.logger_util import logger
from urllib.parse import unquote
from fastapi.responses import PlainTextResponse
from fastapi.responses import J<PERSON>NResponse
from backend.app.utils.time_util import get_china_now
from backend.app.config.config import ENABLE_SAVE_DB
from backend.app.service.bug_evaluation_service import update_bug_evaluation_feedback, update_bug_evaluation_ignore_reason


class BugCheckResponse(BaseModel):
    status: str
    message: str
    data: Dict[str, Any]


import backend.app.utils.weworkapi as weworkapi

TOKEN = "r7EpikcvtkjbnNcfcr"
ENCODING_AES_KEY = "vO4OUOs6n2YP2guo5MboCXPv78ka1C29tDcuitL5B2s"
wx_crypt = weworkapi.WXBizMsgCryptWrapper(TOKEN, ENCODING_AES_KEY, "")


async def callback(msg_signature: str, timestamp: str, nonce: str, echostr: str):
    # Step 1: 解码参数
    echostr = unquote(echostr)

    # Step 2: 验签 + 解密
    ret, plain_text = wx_crypt.verify_url(msg_signature, timestamp, nonce, echostr)

    if ret != weworkapi.WXBizMsgCrypt_OK:
        return PlainTextResponse("Signature verification failed", status_code=403)

    # Step 3: 返回明文响应，不能有引号/bom/换行
    return PlainTextResponse(plain_text.decode("utf-8"), status_code=200)


async def receive_message(request: Request):
    try:
        msg_signature = request.query_params.get("msg_signature")
        timestamp = request.query_params.get("timestamp")
        nonce = request.query_params.get("nonce")

        body = await request.body()
        try:
            json_data = json.loads(body.decode("utf-8"))
            logger.info(f"收到原始JSON数据: {json_data}")
        except Exception as e:
            logger.error(f"解析请求体JSON失败: {e}")
            return PlainTextResponse("Invalid JSON", status_code=400)

        encrypt_text = json_data.get("encrypt")
        if not encrypt_text:
            return PlainTextResponse("Invalid JSON: missing Encrypt", status_code=400)

        ret, plain_text = wx_crypt.key and weworkapi.Prpcrypt(wx_crypt.key).decrypt(encrypt_text,
                                                                                    wx_crypt.m_sReceiveId) or (
                          weworkapi.WXBizMsgCrypt_DecryptAES_Error, None)

        if ret != weworkapi.WXBizMsgCrypt_OK or plain_text is None:
            logger.error(f"消息解密失败，错误码: {ret}")
            return PlainTextResponse("Decrypt failed", status_code=400)

        try:
            msg = json.loads(plain_text.decode("utf-8") if isinstance(plain_text, bytes) else plain_text)
        except Exception as e:
            logger.error(f"解密后内容JSON解析失败: {e}")
            return PlainTextResponse("Parse decrypted message failed", status_code=400)

        logger.info("收到解密消息内容:" + json.dumps(msg, indent=4, ensure_ascii=False))

        # 🚩 判断是否是 /ignore 指令，如果是，返回加密响应，否则继续后续流程
        ignore_response = await ignore_call(msg, nonce, timestamp)
        if ignore_response:
            return JSONResponse(content=ignore_response)

        # 🚀 继续处理正常反馈动作
        bug_id = msg.get("attachment", {}).get("callbackid", "")
        name = msg.get("from", {}).get("name")
        action_value_list = msg.get("attachment", {}).get("actions", [])
        action_value = action_value_list[0].get("value") if action_value_list else ""

        if bug_id and action_value and name:
            logger.info(f"收到BUG_ID: {bug_id}, 操作: {action_value}, 操作人: {name}")
            try:
                task = asyncio.create_task(process_chat_feedback(bug_id, action_value, name))
                task.add_done_callback(lambda t: logger.info(
                    f"聊天反馈处理任务完成，结果: {t.result() if not t.exception() else f'异常: {t.exception()}'}"))
            except Exception as e:
                logger.error(f"创建聊天反馈处理任务失败: {str(e)}")

        return PlainTextResponse("")

    except Exception as e:
        logger.error(f"接收处理异常: {e}")
        return PlainTextResponse("Internal Server Error", status_code=500)


async def ignore_call(msg, nonce, timestamp):
    text = msg.get("text", {}).get("content", "")
    to_user = msg.get("from", {}).get("name", "")
    user_id = msg.get("from", {}).get("userid", "")
    match = re.search(r"/ignore\s+原因：\s*(.+)", text)
    if not match:
        return None  # ⚠️ 不是 /ignore 指令

    ignore_reason = match.group(1).strip()

    url_match = re.search(r"https://tapd\.woa\.com/[^/]+/(\d+)/bug/detail/(\d+)", text)
    if not url_match:
        return build_encrypted_reply("缺陷无法识别，请引用具体的BUG推送卡片或给出具体的缺陷链接", [user_id], user_id,
                                     nonce, timestamp)

    workspace_id, bug_id = url_match.groups()

    flag = await update_bug_evaluation_ignore_reason(workspace_id, bug_id, ignore_reason, to_user)
    if flag:
        return build_encrypted_reply("✅ 缺陷误报原因更新成功", [user_id], user_id, nonce, timestamp)
    else:
        return build_encrypted_reply("缺陷误报原因更新失败", [user_id], user_id, nonce, timestamp)


def build_encrypted_reply(content: str,
                          mentioned_list: list[str] = None,
                          visible_to_user: str = None, nonce=None, timestamp=None) -> dict:
    """
    构建企业微信群机器人格式的文本消息（非 WXBizMsgCrypt）

    Args:
        content: 消息文本内容
        mentioned_list: 要 @ 的成员列表，如 ["zhangsan", "@all"]
        mentioned_mobile_list: 要 @ 的手机号列表，如 ["13800001111", "@all"]
        visible_to_user: 可见成员，多个 userid 用 '|' 分隔

    Returns:
        可直接 post 到 webhook_url 的 dict
    """
    msg = {
        "msgtype": "text",
        "text": {
            "content": content,
            "mentioned_list": [],
            "mentioned_mobile_list": []
        },
        "visible_to_user": ""
    }

    if mentioned_list:
        msg["text"]["mentioned_list"] = mentioned_list
    if visible_to_user:
        msg["visible_to_user"] = visible_to_user
    ret, sEncryptMsg = wx_crypt.EncryptMsg(json.dumps(msg, ensure_ascii=False), nonce, timestamp)
    if ret != 0:
        logger.error("加密错误ret: " + str(ret))
    return json.loads(sEncryptMsg)


async def get_chat_info(get_chat_info_url: str) -> Optional[Dict[str, Any]]:
    """
    从get_chat_info_url获取聊天内容数据

    Args:
        get_chat_info_url: 聊天信息获取URL

    Returns:
        聊天信息字典，失败返回None
    """
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.get(get_chat_info_url)
            response.raise_for_status()

            chat_data = response.json()
            logger.info(f"获取到聊天信息: {json.dumps(chat_data, ensure_ascii=False, indent=2)}")

            return chat_data

    except httpx.RequestError as e:
        logger.error(f"获取聊天信息网络请求失败: {str(e)}")
        return None
    except httpx.HTTPStatusError as e:
        logger.error(f"获取聊天信息HTTP错误: {e.response.status_code} - {e.response.text}")
        return None
    except json.JSONDecodeError as e:
        logger.error(f"解析聊天信息JSON失败: {str(e)}")
        return None
    except Exception as e:
        logger.error(f"获取聊天信息未知错误: {str(e)}")
        return None





async def process_chat_feedback(bug_id: str, action_value: str, operator: str) -> bool:
    """
    处理聊天反馈的主函数

    Args:
        bug_id: BUGID
        action_value: 操作值
        operator: 操作人员

    Returns:
        处理成功返回True，失败返回False
    """
    try:
        update_success = await update_bug_evaluation_feedback(bug_id, action_value, operator)
        if not update_success:
            logger.error(f"更新BUG {bug_id} 评估记录失败")
            return False

        logger.info(f"成功处理聊天反馈，BUG ID: {bug_id}, 操作: {action_value}")
        return True

    except Exception as e:
        logger.error(f"处理聊天反馈时发生未知错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


async def get_bug_content_not_null(bug_data: Dict[str, Any]) -> Dict[str, Any]:
    """获取缺陷内容"""
    filter_bug = {k: v for k, v in bug_data.items() if v not in [None, "", [], {}]}
    return filter_bug


async def get_rules(workspace_id):
    mapping_path = os.path.join(os.path.dirname(__file__), '../rules/workspace_mapping.json')
    with open(mapping_path, 'r', encoding='utf-8') as f:
        workspace_mapping = json.load(f)

    # 获取业务名称，如果没有映射则默认使用"健康"
    business_name = workspace_mapping.get(workspace_id, "健康")

    # 读取规则配置
    rules_path = os.path.join(os.path.dirname(__file__), '../rules/rules.json')
    with open(rules_path, 'r', encoding='utf-8') as f:
        rules_config = json.load(f)

    # 获取业务对应的规则，如果没有特定业务规则则使用默认规则
    business_rules = rules_config.get(business_name, rules_config.get("默认规则", {}))
    # 检查普通字段
    common_field_rules = business_rules.get("普通字段", rules_config.get("默认规则", {}))
    # 检查人员白名单
    people_list = business_rules.get("people", [])
    return common_field_rules, business_rules, people_list


async def check_fields(bug_data: Dict[str, Any], workspace_id: str) -> Tuple[
    bool, List[str], List[DimensionFieldEvaluation], List[TitleFieldEvaluation], List[SuggestFieldEvaluation]]:
    """检查所有字段，包括普通字段和特殊字段"""
    # 读取workspace映射配置
    common_field_rules, business_rules, _ = await get_rules(workspace_id)
    common_missing = []
    for field, required in common_field_rules.items():
        if required and (field not in bug_data or not bug_data.get(field)):
            common_missing.append(field)
    logger.info("缺少字段: " + str(common_missing))
    # 检查特殊字段
    special_field_rules = business_rules.get("特殊字段", {})
    # 获取bug非空信息
    bug_data_not_null = await get_bug_content_not_null(bug_data)
    dimension_evalutions = []
    title_evalutions = []
    suggtest_evalutions = []
    for field, required in special_field_rules.items():
        if required:
            field_content = bug_data.get(field, "")
            if field in ["详细描述"]:
                evaluation: DimensionFieldEvaluation = await get_description_score(field, bug_data_not_null,
                                                                                   workspace_id)
                dimension_evalutions.append(evaluation)
            elif field in ["标题"]:
                evaluation: TitleFieldEvaluation = await get_title_score(field, bug_data_not_null, workspace_id)
                if evaluation.suggest != "":
                    bug_data_not_null[field] = evaluation.suggest
                title_evalutions.append(evaluation)
            elif field in ["优先级"]:
                evaluation: SuggestFieldEvaluation = await get_priority_suggestion(field, bug_data_not_null, workspace_id)
                evaluation.actual = field_content
                suggtest_evalutions.append(evaluation)
            elif field in ["严重程度"]:
                evaluation: SuggestFieldEvaluation = await get_severity_suggestion(field, bug_data_not_null, workspace_id)
                evaluation.actual = field_content
                suggtest_evalutions.append(evaluation)
    # 检查是否全部通过
    common_check = len(common_missing) == 0

    # 只要有任意特殊字段未通过就整体未通过
    special_check = all(eval.passed for eval in dimension_evalutions + title_evalutions)

    all_passed = common_check and special_check

    return all_passed, common_missing, dimension_evalutions, title_evalutions, suggtest_evalutions


async def check_bugs_message(data: Dict[str, Any]) -> BugCheckResponse:
    # 打印接收到的参数
    logger.info(f"接收到的参数: {data}")
    workspace_id = data.get('workspace_id')
    if workspace_id != BUG_EVALUATION_WORKSPACE_ID:
        return BugCheckResponse(
            status="ok",
            message="服务正常运行",
            data={}
        )
    bug_data = tap_client.get_bug_all_pure_message(data.get("workspace_id"), data.get("id"))
    try:
        bug_data = await check_bug_data(bug_data, workspace_id)
        if bug_data != {}:
            pass
            #await save_data(bug_data)
    except Exception as e:
        logger.error(f"[Exception] check_bugs_message 出错: {str(e)}")
    return BugCheckResponse(
        status="ok",
        message="服务正常运行",
        data={}
    )


async def check_bug_data(bug_data, workspace_id=None):
    logger.info(json.dumps(bug_data, ensure_ascii=False, indent=4))
    if bug_data:
        bug_title = bug_data.get("标题", "")
        if "接口自动化" in bug_title:
            logger.info("接口自动化不进行处理")
            return {}
        if "【埋点】" in bug_title:
            logger.info("埋点不进行处理")
            return {}
        try:
            fields_check, common_missing, dimension_evalutions, title_evaluations, suggtest_evaluations = await check_fields(
                bug_data, workspace_id)
            creater = bug_data.get("创建人", "")
            bug_id = bug_data.get("ID", "")
            bug_link = ""
            if workspace_id is None:
                bug_link = bug_data.get("需求链接", "")
            else:
                bug_link = f"https://tapd.woa.com/tapd_fe/{workspace_id}/bug/detail/{bug_id}"

            # 调用send_card_message发送卡片消息并获取返回值
            if creater and not fields_check:
                card_result = await send_card_message(
                    passed=fields_check,
                    creator=creater,
                    bug_title=bug_title,
                    bug_link=bug_link,
                    common_missing=common_missing,
                    dimension_evalutions=dimension_evalutions,
                    title_evaluations=title_evaluations,
                    suggest_evaluations=suggtest_evaluations,
                    bug_id=bug_id
                )

                # 将markdown_content存入bug_data
                if "markdown_content" in card_result:
                    bug_data["markdown_content"] = card_result["markdown_content"]
            bug_data["passed"] = fields_check
            if dimension_evalutions:
                bug_data["纬度评分"] = dimension_evalutions[0].dimension_scores
                bug_data["是否通过"] = dimension_evalutions[0].passed
                bug_data["feedback"] = dimension_evalutions[0].feedback
                bug_data["suggest"] = dimension_evalutions[0].suggest

            # 添加标题评估数据
            if title_evaluations:
                title_eval = title_evaluations[0]
                bug_data["标题纬度评分"] = title_eval.dimension_scores
                bug_data["标题是否通过"] = title_eval.passed
                bug_data["标题反馈"] = title_eval.feedback
                bug_data["标题建议"] = title_eval.suggest
                bug_data["标题阶段1思维过程"] = title_eval.thinking_stage1
                bug_data["标题阶段2思维过程"] = title_eval.thinking_stage2
                # 添加三要素判断结果
                bug_data["标题需要要素"] = title_eval.needed_elements
                bug_data["标题要素判断原因"] = title_eval.element_reason
            if suggtest_evaluations:
                for eval in suggtest_evaluations:
                    bug_data[f"{eval.field}建议"] = eval.suggested
                    bug_data[f"{eval.field}建议原因"] = eval.reason
                    bug_data[f"{eval.field}实际值"] = eval.actual

            # 保存评估结果到数据库
            try:
                if ENABLE_SAVE_DB:
                    evaluation_id = await save_bug_evaluation(
                        bug_data=bug_data,
                        workspace_id=workspace_id or "",
                        overall_passed=fields_check,
                        common_missing=common_missing,
                        dimension_evaluations=dimension_evalutions,
                        title_evaluations=title_evaluations,
                        suggest_evaluations=suggtest_evaluations,
                    )
                    if evaluation_id:
                        logger.info(f"成功保存BUG评估结果到数据库，评估ID: {evaluation_id}")
                        bug_data["evaluation_id"] = evaluation_id
                    else:
                        logger.warning("保存BUG评估结果到数据库失败")
            except Exception as db_error:
                # 数据库存储失败不应影响主流程
                logger.error(f"数据库存储失败，但不影响主流程: {str(db_error)}")
                import traceback
                traceback.print_exc()

            return bug_data
        except Exception as e:
            logger.error(f"[Exception] check_bugs_message 出错: {str(e)}")
            import traceback;
            traceback.print_exc()
            return {}


async def save_data(bug_data):
    # 确保存在logs目录
    logs_dir = Path(os.path.dirname(__file__), "../bug_data")
    logs_dir.mkdir(exist_ok=True)

    # 构造jsonl文件路径，按日期命名
    today = get_china_now().strftime("%Y-%m-%d")
    jsonl_file = logs_dir / f"bug_data_{today}.jsonl"

    # 将bug_data写入jsonl文件
    with open(jsonl_file, "a", encoding="utf-8") as f:
        f.write(json.dumps(bug_data, ensure_ascii=False) + "\n")


async def hello():
    return {"message": "你好，世界！"}


if __name__ == "__main__":
    # https://tapd.woa.com/tapd_fe/20375472/bug/detail/1020375472145724545
    # https://tapd.woa.com/tapd_fe/20452645/bug/detail/1020452645145836009
    # https://tapd.woa.com/tapd_fe/20452645/bug/detail/1020452645145884402
    # https://tapd.woa.com/tapd_fe/20452645/bug/detail/1020452645145903629
    # https://tapd.woa.com/tapd_fe/20426960/bug/detail/1020426960145814756
    # https://tapd.woa.com/tapd_fe/20375472/bug/detail/1020375472145819175
    # https://tapd.woa.com/tapd_fe/20452645/bug/detail/1020452645145936207
    # https://tapd.woa.com/tapd_fe/20426960/bug/detail/1020426960145957822
    # https://tapd.woa.com/tapd_fe/20426960/bug/detail/1020426960145776235
    # https://tapd.woa.com/tapd_fe/20426960/bug/detail/1020426960146021113
    # https://tapd.woa.com/tapd_fe/20452645/bug/detail/1020452645146062553
    # https://tapd.woa.com/tapd_fe/20452645/bug/detail/1020452645146049200
    data = {
        'workspace_id': '20452645',
        'id': "1020452645146219430"
    }
    asyncio.run(check_bugs_message(data))
