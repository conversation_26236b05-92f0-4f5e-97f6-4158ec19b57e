# 详细描述字段JSON解析功能

## 概述

本功能实现了根据不同业务模板，使用正则匹配将BUG详细描述字段从HTML/文本格式转化为结构化JSON格式的功能。

## 功能特点

1. **多业务模板支持**：支持健康、医药SaaS、米影、道真等不同业务的模板格式
2. **HTML自动转换**：自动将HTML标签转换为Markdown格式后进行解析
3. **智能字段识别**：使用正则表达式智能识别和提取各个结构化字段
4. **容错处理**：当无法解析出结构化数据时，自动回退到原有的HTML转MD方式

## 支持的业务模板

### 1. 健康业务 (JIANKANG_WORKSPACE_ID, YIBAO_WORKSPACE_ID)
- 【条件】
- 【机型】
- 【步骤】
- 【预期结果】
- 【实际结果】

### 2. 医药SaaS业务 (YIYAO_SAAS_WORKSPACE_ID)
- 【前提条件】
- 【问题现象】
- 【重现步骤】
- 【预期结果】
- 【其他补充】

### 3. 米影业务 (MIYING_WORKSPACE_ID)
- 【环境】
- 【前置条件】
- 【机型】
- 【步骤】
- 【现象】
- 【重现规律】
- 【预期结果】

### 4. 道真业务 (DAOZHEN_WORKSPACE_ID)
- 【环境】
- 【机型】
- 【步骤】
- 【现象】
- 【重现规律】
- 【预期结果】

## 核心函数

### `parse_description_to_json(description: str, workspace_id: str) -> dict`

将详细描述字段转化为JSON格式。

**参数：**
- `description`: 详细描述字段内容（支持HTML格式）
- `workspace_id`: 工作区ID，用于确定业务模板

**返回值：**
- `dict`: 解析后的JSON格式数据，键为字段名（如"【条件】"），值为字段内容

### `get_field_patterns_by_workspace(workspace_id: str) -> dict`

根据workspace_id返回对应的字段正则匹配模式。

## 使用示例

```python
from backend.app.utils.tapd import parse_description_to_json

# HTML格式的详细描述
description = """
<div>
<p><strong>【条件】</strong></p>
<p>测试环境，用户已登录</p>

<p><strong>【机型】</strong></p>
<p>iPhone 12 iOS 18</p>

<p><strong>【步骤】</strong></p>
<p>1. 打开App首页 -> 点击"我的"<br/>
2. 点击头像 -> 进入编辑页</p>

<p><strong>【预期结果】</strong></p>
<p>页面同步显示"已保存"提示</p>

<p><strong>【实际结果】</strong></p>
<p>页面没有任何提示，但昵称已更新</p>
</div>
"""

# 解析为JSON格式
result = parse_description_to_json(description, "20375472")

# 输出结果
{
  "【条件】": "测试环境，用户已登录",
  "【机型】": "iPhone 12 iOS 18", 
  "【步骤】": "1. 打开App首页 -> 点击\"我的\" 2. 点击头像 -> 进入编辑页",
  "【预期结果】": "页面同步显示\"已保存\"提示",
  "【实际结果】": "页面没有任何提示，但昵称已更新"
}
```

## 集成到现有流程

该功能已集成到 `TAPDClient.process_bug_data()` 方法中：

1. 当调用 `process_bug_data()` 时，会自动尝试解析详细描述字段
2. 如果成功解析出结构化数据，则将JSON格式存储到详细描述字段
3. 如果解析失败，则回退到原有的HTML转MD方式
4. 最终结果会被限制在 `DESCRIPTION_MAX_LENGTH` 长度内

## 技术实现

1. **HTML转换**：使用 `md_with_fix()` 函数将HTML转换为Markdown格式
2. **字段定位**：通过正则表达式找到所有字段标题的位置
3. **内容提取**：按字段在文本中的出现顺序提取内容，避免截断问题
4. **内容清理**：移除多余的换行符和空格，规范化输出格式

## 注意事项

1. 字段标题必须使用全角中括号格式，如"【条件】"
2. 支持字段标题后跟冒号或不跟冒号的格式
3. 字段内容会自动清理多余的空白字符
4. 当无法识别任何结构化字段时，会返回空字典并回退到原有处理方式
